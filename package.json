{"name": "gemini-cli", "version": "1.0.0", "description": "A command-line interface for Google's Gemini AI models", "main": "src/index.js", "bin": {"gemini": "src/index.js"}, "scripts": {"start": "node src/index.js", "build": "pkg . --out-path dist", "build:all": "pkg . --targets node18-win-x64,node18-macos-x64,node18-linux-x64 --out-path dist", "test": "node test/test.js"}, "keywords": ["gemini", "ai", "cli", "google", "chatbot", "terminal"], "author": "Your Name", "license": "MIT", "dependencies": {"@google/generative-ai": "^0.17.1", "commander": "^11.1.0", "chalk": "^4.1.2", "inquirer": "^8.2.6", "ora": "^5.4.1", "highlight.js": "^11.9.0", "marked": "^9.1.6", "marked-terminal": "^6.2.0", "conf": "^10.2.0", "boxen": "^5.1.2"}, "devDependencies": {"pkg": "^5.8.1"}, "pkg": {"scripts": "src/**/*.js", "assets": ["node_modules/@google/generative-ai/**/*"], "targets": ["node18-win-x64", "node18-macos-x64", "node18-linux-x64"], "outputPath": "dist"}, "engines": {"node": ">=16.0.0"}}