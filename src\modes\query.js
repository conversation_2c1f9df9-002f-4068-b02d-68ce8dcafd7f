const chalk = require('chalk');
const ora = require('ora');
const fs = require('fs').promises;
const path = require('path');

class QueryMode {
  constructor(geminiService, formatter, options = {}) {
    this.gemini = geminiService;
    this.formatter = formatter;
    this.options = options;
  }

  /**
   * Execute a single query
   * @param {string} question - The question to ask
   */
  async execute(question) {
    const spinner = ora('Processing your query...').start();

    try {
      let response;

      // Check if image is provided
      if (this.options.image) {
        response = await this.handleImageQuery(question, this.options.image);
      } else {
        response = await this.handleTextQuery(question);
      }

      spinner.stop();

      // Format and display response
      const format = this.options.format || 'markdown';
      const formattedResponse = this.formatter.format(response, format, {
        model: this.options.model || 'gemini-pro',
        temperature: this.options.temperature || '0.7',
        query: question
      });

      console.log(formattedResponse);

    } catch (error) {
      spinner.stop();
      console.error(this.formatter.formatError('Failed to process query', error));
      process.exit(1);
    }
  }

  /**
   * Handle text-only query
   * @param {string} question - The question to ask
   * @returns {Promise<string>} Response from Gemini
   */
  async handleTextQuery(question) {
    return await this.gemini.generateText(question, {
      model: this.options.model,
      temperature: this.options.temperature
    });
  }

  /**
   * Handle query with image
   * @param {string} question - The question to ask
   * @param {string} imagePath - Path to the image file
   * @returns {Promise<string>} Response from Gemini
   */
  async handleImageQuery(question, imagePath) {
    // Validate image file
    await this.validateImageFile(imagePath);

    return await this.gemini.generateWithImage(question, imagePath, {
      model: this.options.model || 'gemini-pro-vision',
      temperature: this.options.temperature
    });
  }

  /**
   * Validate image file exists and is supported
   * @param {string} imagePath - Path to the image file
   */
  async validateImageFile(imagePath) {
    try {
      // Check if file exists
      await fs.access(imagePath);

      // Check if it's a supported image format
      if (!this.gemini.isSupportedImage(imagePath)) {
        throw new Error(`Unsupported image format. Supported formats: JPG, JPEG, PNG, GIF, WebP`);
      }

      // Check file size (Gemini has limits)
      const stats = await fs.stat(imagePath);
      const fileSizeMB = stats.size / (1024 * 1024);
      
      if (fileSizeMB > 20) {
        throw new Error(`Image file too large (${fileSizeMB.toFixed(1)}MB). Maximum size is 20MB.`);
      }

    } catch (error) {
      if (error.code === 'ENOENT') {
        throw new Error(`Image file not found: ${imagePath}`);
      }
      throw error;
    }
  }

  /**
   * Process query from stdin (for piped input)
   * @returns {Promise<string>} Input from stdin
   */
  static async readFromStdin() {
    return new Promise((resolve, reject) => {
      let input = '';
      
      process.stdin.setEncoding('utf8');
      
      process.stdin.on('readable', () => {
        let chunk;
        while ((chunk = process.stdin.read()) !== null) {
          input += chunk;
        }
      });
      
      process.stdin.on('end', () => {
        resolve(input.trim());
      });
      
      process.stdin.on('error', reject);
    });
  }

  /**
   * Handle piped input queries
   * @param {Object} options - Query options
   */
  static async handlePipedInput(options = {}) {
    try {
      const input = await QueryMode.readFromStdin();
      
      if (!input) {
        console.error(chalk.red('No input provided'));
        process.exit(1);
      }

      // Create temporary query mode instance
      const { GeminiService } = require('../services/gemini');
      const { ConfigManager } = require('../utils/config');
      const { OutputFormatter } = require('../utils/formatter');
      
      const config = new ConfigManager();
      const apiKey = config.get('apiKey');
      
      if (!apiKey) {
        console.error(chalk.red('❌ API key not configured. Run: gemini config -k YOUR_API_KEY'));
        process.exit(1);
      }
      
      const gemini = new GeminiService(apiKey);
      const formatter = new OutputFormatter();
      const queryMode = new QueryMode(gemini, formatter, options);
      
      await queryMode.execute(input);
      
    } catch (error) {
      console.error(chalk.red('Error processing piped input:'), error.message);
      process.exit(1);
    }
  }
}

module.exports = { QueryMode };
