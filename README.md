# Gemini CLI

A powerful command-line interface that brings Google's Gemini AI directly to your terminal. No need to install Node.js or npm - just download and run!

## Features

- 🚀 **Standalone Executable** - No Node.js or npm installation required
- 💬 **Interactive Chat Mode** - Have conversations with Gemini AI
- ❓ **Single Query Mode** - Ask one-off questions quickly
- 🖼️ **Image Analysis** - Upload images for Gemini Vision analysis
- 🎨 **Multiple Output Formats** - Markdown, plain text, or JSON output
- ⚙️ **Configurable** - Save API keys and preferences
- 🌈 **Syntax Highlighting** - Beautiful code highlighting in responses
- 📱 **Cross-Platform** - Works on Windows, macOS, and Linux

## Quick Start

### 1. Download

Download the appropriate executable for your platform from the [releases page](https://github.com/your-username/gemini-cli/releases):

- **Windows**: `gemini-win.exe`
- **macOS**: `gemini-macos`
- **Linux**: `gemini-linux`

### 2. Get Google AI API Key

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key for the next step

### 3. Configure

```bash
# Set your API key
./gemini config -k YOUR_API_KEY

# Verify configuration
./gemini config --show
```

### 4. Start Using

```bash
# Interactive chat mode
./gemini chat

# Ask a single question
./gemini ask "Explain quantum computing"

# Ask about an image
./gemini ask "What's in this image?" --image photo.jpg

# Get help
./gemini --help
```

## Usage Examples

### Interactive Chat
```bash
./gemini chat
```
Start a conversation with Gemini. Type `help` for chat commands.

### Single Queries
```bash
# Simple question
./gemini ask "What is the capital of France?"

# Code generation
./gemini ask "Write a Python function to calculate fibonacci numbers"

# With specific model
./gemini ask "Explain machine learning" --model gemini-pro

# Different output format
./gemini ask "List 5 programming languages" --format json
```

### Image Analysis
```bash
# Analyze an image
./gemini ask "Describe this image" --image photo.jpg

# Extract text from image
./gemini ask "What text is in this image?" --image screenshot.png
```

### Configuration
```bash
# Set API key
./gemini config --key YOUR_API_KEY

# Set default model
./gemini config --model gemini-pro-vision

# Show current config
./gemini config --show
```

### Advanced Usage
```bash
# Pipe input
echo "Explain Docker" | ./gemini ask

# Custom temperature (creativity level)
./gemini ask "Write a creative story" --temperature 0.9

# List available models
./gemini models
```

## Commands

### `gemini config`
Configure API settings
- `-k, --key <apiKey>` - Set your Google AI API key
- `-m, --model <model>` - Set default model
- `-s, --show` - Show current configuration

### `gemini chat`
Start interactive chat mode
- `-m, --model <model>` - Model to use
- `-f, --format <format>` - Output format (markdown, plain, json)
- `-t, --temperature <temp>` - Temperature (0.0-1.0)

### `gemini ask <question>`
Ask a single question
- `-m, --model <model>` - Model to use
- `-f, --format <format>` - Output format
- `-t, --temperature <temp>` - Temperature
- `-i, --image <path>` - Include image file

### `gemini models`
List available Gemini models

## Available Models

- **gemini-pro** - Best for text-only prompts
- **gemini-pro-vision** - Best for text and image prompts

## Output Formats

- **markdown** (default) - Formatted text with syntax highlighting
- **plain** - Plain text without formatting
- **json** - Structured JSON response

## Configuration

Configuration is stored in:
- **Windows**: `%USERPROFILE%\.gemini-cli\config.json`
- **macOS/Linux**: `~/.gemini-cli/config.json`

## Building from Source

If you want to build the executable yourself:

```bash
# Clone the repository
git clone https://github.com/your-username/gemini-cli.git
cd gemini-cli

# Install dependencies
npm install

# Build executables for all platforms
npm run build:all

# Or build for current platform only
npm run build
```

## Troubleshooting

### API Key Issues
- Make sure your API key is valid and has Gemini API access
- Check your Google Cloud billing is enabled
- Verify the key with: `./gemini config --show`

### Image Upload Issues
- Supported formats: JPG, JPEG, PNG, GIF, WebP
- Maximum file size: 20MB
- Make sure the file path is correct

### Permission Issues (macOS/Linux)
```bash
# Make the file executable
chmod +x gemini-macos  # or gemini-linux
```

## License

MIT License - see [LICENSE](LICENSE) file for details.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
