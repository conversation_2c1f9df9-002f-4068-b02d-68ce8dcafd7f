const Conf = require('conf');
const path = require('path');
const os = require('os');

class ConfigManager {
  constructor() {
    this.config = new Conf({
      projectName: 'gemini-cli',
      configName: 'config',
      cwd: path.join(os.homedir(), '.gemini-cli'),
      defaults: {
        model: 'gemini-pro',
        outputFormat: 'markdown',
        temperature: 0.7,
        maxTokens: 8192
      }
    });
  }

  /**
   * Get a configuration value
   * @param {string} key - Configuration key
   * @returns {*} Configuration value
   */
  get(key) {
    return this.config.get(key);
  }

  /**
   * Set a configuration value
   * @param {string} key - Configuration key
   * @param {*} value - Configuration value
   */
  set(key, value) {
    this.config.set(key, value);
  }

  /**
   * Get all configuration values
   * @returns {Object} All configuration values
   */
  getAll() {
    return this.config.store;
  }

  /**
   * Delete a configuration value
   * @param {string} key - Configuration key
   */
  delete(key) {
    this.config.delete(key);
  }

  /**
   * Clear all configuration
   */
  clear() {
    this.config.clear();
  }

  /**
   * Check if API key is configured
   * @returns {boolean} True if API key exists
   */
  hasApiKey() {
    const apiKey = this.get('apiKey');
    return apiKey && apiKey.length > 0;
  }

  /**
   * Get the configuration file path
   * @returns {string} Path to configuration file
   */
  getConfigPath() {
    return this.config.path;
  }
}

module.exports = { ConfigManager };
