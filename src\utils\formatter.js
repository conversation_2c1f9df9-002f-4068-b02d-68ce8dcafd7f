const chalk = require('chalk');
const marked = require('marked');
const TerminalRenderer = require('marked-terminal');
const hljs = require('highlight.js');

class OutputFormatter {
  constructor() {
    // Configure marked for terminal output
    marked.setOptions({
      renderer: new TerminalRenderer({
        code: chalk.yellow,
        blockquote: chalk.gray.italic,
        html: chalk.gray,
        heading: chalk.green.bold,
        firstHeading: chalk.magenta.underline.bold,
        hr: chalk.reset,
        listitem: chalk.reset,
        list: chalk.reset,
        table: chalk.reset,
        paragraph: chalk.reset,
        strong: chalk.bold,
        em: chalk.italic,
        codespan: chalk.yellow,
        del: chalk.dim.gray.strikethrough,
        link: chalk.blue,
        href: chalk.blue.underline
      }),
      highlight: function(code, lang) {
        if (lang && hljs.getLanguage(lang)) {
          try {
            return hljs.highlight(code, { language: lang }).value;
          } catch (err) {
            // Fall back to auto-detection
          }
        }
        try {
          return hljs.highlightAuto(code).value;
        } catch (err) {
          return code;
        }
      }
    });
  }

  /**
   * Format output based on the specified format
   * @param {string} content - Content to format
   * @param {string} format - Output format (markdown, plain, json)
   * @param {Object} metadata - Additional metadata
   * @returns {string} Formatted content
   */
  format(content, format = 'markdown', metadata = {}) {
    switch (format.toLowerCase()) {
      case 'json':
        return this.formatJson(content, metadata);
      case 'plain':
        return this.formatPlain(content);
      case 'markdown':
      default:
        return this.formatMarkdown(content);
    }
  }

  /**
   * Format content as markdown with syntax highlighting
   * @param {string} content - Content to format
   * @returns {string} Formatted markdown
   */
  formatMarkdown(content) {
    try {
      return marked(content);
    } catch (error) {
      // Fallback to plain text if markdown parsing fails
      return content;
    }
  }

  /**
   * Format content as plain text
   * @param {string} content - Content to format
   * @returns {string} Plain text content
   */
  formatPlain(content) {
    // Remove markdown formatting
    return content
      .replace(/\*\*(.*?)\*\*/g, '$1')  // Bold
      .replace(/\*(.*?)\*/g, '$1')      // Italic
      .replace(/`(.*?)`/g, '$1')        // Inline code
      .replace(/```[\s\S]*?```/g, (match) => {
        // Code blocks - remove language identifier and backticks
        return match.replace(/```\w*\n?/g, '').replace(/```/g, '');
      })
      .replace(/#{1,6}\s/g, '')         // Headers
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1'); // Links
  }

  /**
   * Format content as JSON
   * @param {string} content - Content to format
   * @param {Object} metadata - Additional metadata
   * @returns {string} JSON formatted content
   */
  formatJson(content, metadata = {}) {
    const output = {
      response: content,
      timestamp: new Date().toISOString(),
      ...metadata
    };
    return JSON.stringify(output, null, 2);
  }

  /**
   * Format error messages
   * @param {string} message - Error message
   * @param {Error} error - Error object
   * @returns {string} Formatted error
   */
  formatError(message, error = null) {
    let output = chalk.red('❌ ') + chalk.red.bold('Error: ') + message;
    
    if (error && error.stack) {
      output += '\n' + chalk.gray(error.stack);
    }
    
    return output;
  }

  /**
   * Format success messages
   * @param {string} message - Success message
   * @returns {string} Formatted success message
   */
  formatSuccess(message) {
    return chalk.green('✓ ') + message;
  }

  /**
   * Format warning messages
   * @param {string} message - Warning message
   * @returns {string} Formatted warning message
   */
  formatWarning(message) {
    return chalk.yellow('⚠ ') + message;
  }

  /**
   * Format info messages
   * @param {string} message - Info message
   * @returns {string} Formatted info message
   */
  formatInfo(message) {
    return chalk.blue('ℹ ') + message;
  }

  /**
   * Create a separator line
   * @param {string} char - Character to use for separator
   * @param {number} length - Length of separator
   * @returns {string} Separator line
   */
  separator(char = '─', length = 50) {
    return chalk.gray(char.repeat(length));
  }

  /**
   * Format a header with box
   * @param {string} title - Header title
   * @returns {string} Formatted header
   */
  header(title) {
    const boxen = require('boxen');
    return boxen(chalk.bold(title), {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'blue'
    });
  }
}

module.exports = { OutputFormatter };
