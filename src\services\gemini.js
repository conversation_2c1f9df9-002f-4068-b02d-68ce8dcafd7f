const { GoogleGenerativeAI } = require('@google/generative-ai');
const fs = require('fs').promises;
const path = require('path');

class GeminiService {
  constructor(apiKey) {
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.defaultModel = 'gemini-pro';
  }

  /**
   * Get a generative model instance
   * @param {string} modelName - Name of the model to use
   * @param {Object} config - Model configuration
   * @returns {Object} Model instance
   */
  getModel(modelName = this.defaultModel, config = {}) {
    const defaultConfig = {
      temperature: 0.7,
      topK: 40,
      topP: 0.95,
      maxOutputTokens: 8192,
    };

    return this.genAI.getGenerativeModel({
      model: modelName,
      generationConfig: { ...defaultConfig, ...config }
    });
  }

  /**
   * Generate content from text prompt
   * @param {string} prompt - The text prompt
   * @param {Object} options - Generation options
   * @returns {Promise<string>} Generated response
   */
  async generateText(prompt, options = {}) {
    try {
      const model = this.getModel(options.model, {
        temperature: options.temperature ? parseFloat(options.temperature) : undefined
      });

      const result = await model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      throw new Error(`Gemini API error: ${error.message}`);
    }
  }

  /**
   * Generate content with image
   * @param {string} prompt - The text prompt
   * @param {string} imagePath - Path to the image file
   * @param {Object} options - Generation options
   * @returns {Promise<string>} Generated response
   */
  async generateWithImage(prompt, imagePath, options = {}) {
    try {
      const model = this.getModel('gemini-pro-vision', {
        temperature: options.temperature ? parseFloat(options.temperature) : undefined
      });

      // Read and encode image
      const imageData = await fs.readFile(imagePath);
      const mimeType = this.getMimeType(imagePath);

      const imagePart = {
        inlineData: {
          data: imageData.toString('base64'),
          mimeType: mimeType
        }
      };

      const result = await model.generateContent([prompt, imagePart]);
      const response = await result.response;
      return response.text();
    } catch (error) {
      throw new Error(`Gemini Vision API error: ${error.message}`);
    }
  }

  /**
   * Start a chat session
   * @param {Object} options - Chat options
   * @returns {Object} Chat session
   */
  startChat(options = {}) {
    const model = this.getModel(options.model, {
      temperature: options.temperature ? parseFloat(options.temperature) : undefined
    });

    return model.startChat({
      history: options.history || [],
      generationConfig: {
        maxOutputTokens: 8192,
      }
    });
  }

  /**
   * List available models
   * @returns {Promise<Array>} List of available models
   */
  async listModels() {
    try {
      const models = await this.genAI.listModels();
      return models.map(model => ({
        name: model.name,
        description: model.description || '',
        supportedGenerationMethods: model.supportedGenerationMethods || []
      }));
    } catch (error) {
      // Fallback to known models if API call fails
      return [
        {
          name: 'gemini-pro',
          description: 'Best model for text-only prompts',
          supportedGenerationMethods: ['generateContent']
        },
        {
          name: 'gemini-pro-vision',
          description: 'Best model for text and image prompts',
          supportedGenerationMethods: ['generateContent']
        }
      ];
    }
  }

  /**
   * Get MIME type from file extension
   * @param {string} filePath - Path to the file
   * @returns {string} MIME type
   */
  getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp'
    };
    return mimeTypes[ext] || 'image/jpeg';
  }

  /**
   * Validate if file is a supported image
   * @param {string} filePath - Path to the file
   * @returns {boolean} True if supported image format
   */
  isSupportedImage(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return ['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext);
  }
}

module.exports = { GeminiService };
