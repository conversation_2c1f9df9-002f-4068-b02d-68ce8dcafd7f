const inquirer = require('inquirer');
const chalk = require('chalk');
const ora = require('ora');

class ChatMode {
  constructor(geminiService, formatter, options = {}) {
    this.gemini = geminiService;
    this.formatter = formatter;
    this.options = options;
    this.chat = null;
    this.conversationHistory = [];
  }

  /**
   * Start interactive chat session
   */
  async start() {
    console.log(this.formatter.header('Gemini AI Chat'));
    console.log(chalk.gray('Type "exit", "quit", or press Ctrl+C to end the conversation'));
    console.log(chalk.gray('Type "clear" to clear conversation history'));
    console.log(chalk.gray('Type "help" for available commands'));
    console.log(this.formatter.separator());

    // Initialize chat session
    this.chat = this.gemini.startChat({
      model: this.options.model,
      temperature: this.options.temperature,
      history: this.conversationHistory
    });

    while (true) {
      try {
        const { message } = await inquirer.prompt([
          {
            type: 'input',
            name: 'message',
            message: chalk.blue('You:'),
            validate: (input) => {
              if (!input.trim()) {
                return 'Please enter a message';
              }
              return true;
            }
          }
        ]);

        const trimmedMessage = message.trim();

        // Handle special commands
        if (this.handleCommand(trimmedMessage)) {
          continue;
        }

        // Send message to Gemini
        await this.sendMessage(trimmedMessage);

      } catch (error) {
        if (error.isTtyError || error.name === 'ExitPromptError') {
          // User pressed Ctrl+C
          break;
        }
        console.error(this.formatter.formatError('Failed to process message', error));
      }
    }

    console.log(chalk.yellow('\nGoodbye! 👋'));
  }

  /**
   * Handle special chat commands
   * @param {string} message - User message
   * @returns {boolean} True if command was handled
   */
  handleCommand(message) {
    const command = message.toLowerCase();

    switch (command) {
      case 'exit':
      case 'quit':
        console.log(chalk.yellow('\nGoodbye! 👋'));
        process.exit(0);
        break;

      case 'clear':
        this.conversationHistory = [];
        this.chat = this.gemini.startChat({
          model: this.options.model,
          temperature: this.options.temperature,
          history: []
        });
        console.log(this.formatter.formatSuccess('Conversation history cleared'));
        return true;

      case 'help':
        this.showHelp();
        return true;

      case 'history':
        this.showHistory();
        return true;

      case 'config':
        this.showConfig();
        return true;

      default:
        return false;
    }
  }

  /**
   * Send message to Gemini and display response
   * @param {string} message - User message
   */
  async sendMessage(message) {
    const spinner = ora('Thinking...').start();

    try {
      const result = await this.chat.sendMessage(message);
      const response = await result.response;
      const text = response.text();

      spinner.stop();

      // Display response
      console.log(chalk.green('\nGemini:'));
      console.log(this.formatter.format(text, this.options.format || 'markdown'));
      console.log(this.formatter.separator());

      // Update conversation history
      this.conversationHistory.push(
        { role: 'user', parts: [{ text: message }] },
        { role: 'model', parts: [{ text: text }] }
      );

    } catch (error) {
      spinner.stop();
      console.error(this.formatter.formatError('Failed to get response from Gemini', error));
    }
  }

  /**
   * Show help information
   */
  showHelp() {
    console.log(chalk.blue('\nAvailable Commands:'));
    console.log(chalk.gray('  exit, quit    - Exit the chat'));
    console.log(chalk.gray('  clear         - Clear conversation history'));
    console.log(chalk.gray('  help          - Show this help message'));
    console.log(chalk.gray('  history       - Show conversation history'));
    console.log(chalk.gray('  config        - Show current configuration'));
    console.log(this.formatter.separator());
  }

  /**
   * Show conversation history
   */
  showHistory() {
    if (this.conversationHistory.length === 0) {
      console.log(this.formatter.formatInfo('No conversation history'));
      return;
    }

    console.log(chalk.blue('\nConversation History:'));
    this.conversationHistory.forEach((entry, index) => {
      const role = entry.role === 'user' ? chalk.blue('You') : chalk.green('Gemini');
      const text = entry.parts[0]?.text || '';
      const preview = text.length > 100 ? text.substring(0, 100) + '...' : text;
      console.log(`${index + 1}. ${role}: ${chalk.gray(preview)}`);
    });
    console.log(this.formatter.separator());
  }

  /**
   * Show current configuration
   */
  showConfig() {
    console.log(chalk.blue('\nCurrent Session Configuration:'));
    console.log(`Model: ${this.options.model || 'gemini-pro'}`);
    console.log(`Format: ${this.options.format || 'markdown'}`);
    console.log(`Temperature: ${this.options.temperature || '0.7'}`);
    console.log(this.formatter.separator());
  }
}

module.exports = { ChatMode };
