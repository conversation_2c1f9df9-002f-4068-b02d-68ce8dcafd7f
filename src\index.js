#!/usr/bin/env node

const { Command } = require('commander');
const chalk = require('chalk');
const packageJson = require('../package.json');
const { GeminiService } = require('./services/gemini');
const { ConfigManager } = require('./utils/config');
const { OutputFormatter } = require('./utils/formatter');
const { ChatMode } = require('./modes/chat');
const { QueryMode } = require('./modes/query');

const program = new Command();
const config = new ConfigManager();
const formatter = new OutputFormatter();

program
  .name('gemini')
  .description('CLI interface for Google Gemini AI')
  .version(packageJson.version);

// Configure API key
program
  .command('config')
  .description('Configure Gemini API settings')
  .option('-k, --key <apiKey>', 'Set your Google AI API key')
  .option('-m, --model <model>', 'Set default model (gemini-pro, gemini-pro-vision)')
  .option('-s, --show', 'Show current configuration')
  .action(async (options) => {
    if (options.key) {
      config.set('apiKey', options.key);
      console.log(chalk.green('✓ API key saved successfully'));
    }
    
    if (options.model) {
      config.set('model', options.model);
      console.log(chalk.green(`✓ Default model set to: ${options.model}`));
    }
    
    if (options.show) {
      const currentConfig = config.getAll();
      console.log(chalk.blue('Current Configuration:'));
      console.log(`API Key: ${currentConfig.apiKey ? '***' + currentConfig.apiKey.slice(-4) : 'Not set'}`);
      console.log(`Model: ${currentConfig.model || 'gemini-pro'}`);
      console.log(`Output Format: ${currentConfig.outputFormat || 'markdown'}`);
    }
  });

// Chat mode
program
  .command('chat')
  .description('Start interactive chat with Gemini')
  .option('-m, --model <model>', 'Model to use (gemini-pro, gemini-pro-vision)')
  .option('-f, --format <format>', 'Output format (markdown, plain, json)')
  .option('-t, --temperature <temp>', 'Temperature for responses (0.0-1.0)')
  .action(async (options) => {
    try {
      const apiKey = config.get('apiKey');
      if (!apiKey) {
        console.log(chalk.red('❌ API key not configured. Run: gemini config -k YOUR_API_KEY'));
        process.exit(1);
      }
      
      const gemini = new GeminiService(apiKey);
      const chatMode = new ChatMode(gemini, formatter, options);
      await chatMode.start();
    } catch (error) {
      console.error(chalk.red('Error starting chat:'), error.message);
      process.exit(1);
    }
  });

// Single query mode
program
  .command('ask <question>')
  .description('Ask a single question to Gemini')
  .option('-m, --model <model>', 'Model to use (gemini-pro, gemini-pro-vision)')
  .option('-f, --format <format>', 'Output format (markdown, plain, json)')
  .option('-t, --temperature <temp>', 'Temperature for responses (0.0-1.0)')
  .option('-i, --image <path>', 'Include image file in the query')
  .action(async (question, options) => {
    try {
      const apiKey = config.get('apiKey');
      if (!apiKey) {
        console.log(chalk.red('❌ API key not configured. Run: gemini config -k YOUR_API_KEY'));
        process.exit(1);
      }
      
      const gemini = new GeminiService(apiKey);
      const queryMode = new QueryMode(gemini, formatter, options);
      await queryMode.execute(question);
    } catch (error) {
      console.error(chalk.red('Error processing query:'), error.message);
      process.exit(1);
    }
  });

// List available models
program
  .command('models')
  .description('List available Gemini models')
  .action(async () => {
    try {
      const apiKey = config.get('apiKey');
      if (!apiKey) {
        console.log(chalk.red('❌ API key not configured. Run: gemini config -k YOUR_API_KEY'));
        process.exit(1);
      }
      
      const gemini = new GeminiService(apiKey);
      const models = await gemini.listModels();
      
      console.log(chalk.blue('Available Models:'));
      models.forEach(model => {
        console.log(`  ${chalk.green('•')} ${model.name}`);
        if (model.description) {
          console.log(`    ${chalk.gray(model.description)}`);
        }
      });
    } catch (error) {
      console.error(chalk.red('Error fetching models:'), error.message);
    }
  });

// Show help if no command provided
if (process.argv.length === 2) {
  program.help();
}

program.parse();
